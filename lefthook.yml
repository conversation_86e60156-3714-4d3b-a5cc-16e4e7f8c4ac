# Lefthook configuration file
# See https://github.com/evilmartians/lefthook for more information

# Pre-commit hooks
pre-commit:
  parallel: true
  commands:
    biome-format:
      glob: "*.{js,jsx,ts,tsx,vue,json}"
      run: bun biome format --write {staged_files}
    biome-lint:
      glob: "*.{js,jsx,ts,tsx,vue}"
      run: bun biome lint {staged_files}
    biome-check:
      glob: "*.{js,jsx,ts,tsx,vue,json}"
      run: bun biome check --write {staged_files}

# Pre-push hooks
pre-push:
  parallel: true
  commands:
    biome-ci:
      run: bun biome ci .

# Install hooks automatically on lefthook install
skip_output:
  - meta
  - success
  - summary

# Automatically fix simple issues
fix_staged_files: true
