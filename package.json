{"name": "defi-agent", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "npx nuxthub preview", "deploy": "npx nuxthub deploy", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxt/eslint": "^1.3.0", "@nuxthub/core": "^0.8.25", "nuxt": "^3.16.2", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@nuxt/eslint-config": "^1.3.0", "eslint": "^9.25.1", "typescript": "^5.8.3", "vue-tsc": "^2.2.10", "wrangler": "^4.12.1"}}